# -*- coding: utf-8 -*-
"""
南京市纪委监委新闻爬虫测试版本（不依赖pandas）
用于测试基本爬取功能
"""

import requests
from bs4 import BeautifulSoup
from lxml import html
import re
from datetime import datetime
import time
from urllib.parse import urljoin
import csv

# 配置
START_URL = "https://jw.nanjing.gov.cn/xwzx/yw/index.html"
BASE_URL = "https://jw.nanjing.gov.cn"
TARGET_DATE_RANGE = ['2025-05', '2025-06', '2025-07']
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

def fetch_webpage(url):
    """获取网页内容"""
    try:
        # 禁用代理
        proxies = {'http': None, 'https': None}
        response = requests.get(url, headers=HEADERS, timeout=30, proxies=proxies)
        response.raise_for_status()
        
        if response.encoding == 'ISO-8859-1':
            response.encoding = response.apparent_encoding or 'utf-8'
        elif not response.encoding:
            response.encoding = 'utf-8'
            
        print(f"✓ 成功获取页面: {url}")
        return True, response.text
    except Exception as e:
        print(f"✗ 获取页面失败: {url}, 错误: {str(e)}")
        return False, str(e)

def extract_news_links(html_content, base_url):
    """提取新闻链接"""
    try:
        tree = html.fromstring(html_content)
        news_items = tree.xpath("//ul[@class='m_right_ul']/li")
        
        links = []
        for item in news_items:
            # 提取标题链接
            title_elements = item.xpath(".//a")
            if not title_elements:
                continue
                
            title_text = title_elements[0].text_content().strip()
            title_link = title_elements[0].get('href')
            
            if not title_link:
                continue
                
            # 提取日期
            date_elements = item.xpath(".//span[last()]")
            if not date_elements:
                continue
                
            date_text = date_elements[0].text_content().strip()
            
            # 检查是否属于目标日期范围
            target_found = False
            for target_date in TARGET_DATE_RANGE:
                if target_date in date_text:
                    target_found = True
                    break
                    
            if not target_found:
                continue
            
            # 处理相对URL
            full_url = urljoin(base_url, title_link)
            
            links.append({
                'url': full_url,
                'title': title_text,
                'date': date_text
            })

        print(f"  找到 {len(links)} 条目标日期范围的新闻")
        return links
        
    except Exception as e:
        print(f"  提取链接失败: {str(e)}")
        return []

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 80)
    print("南京市纪委监委新闻爬虫测试")
    print("=" * 80)
    
    # 测试获取首页
    print("\n1. 测试获取首页...")
    success, content = fetch_webpage(START_URL)
    if not success:
        print("✗ 首页获取失败，测试终止")
        return False
    
    # 测试提取新闻链接
    print("\n2. 测试提取新闻链接...")
    links = extract_news_links(content, BASE_URL)
    if not links:
        print("✗ 未找到任何目标日期的新闻链接")
        return False
    
    print(f"✓ 成功提取 {len(links)} 个新闻链接")
    
    # 显示前几个链接
    print("\n3. 前5个新闻链接:")
    for i, link in enumerate(links[:5], 1):
        print(f"  {i}. {link['title']} ({link['date']})")
        print(f"     URL: {link['url']}")
    
    # 测试获取一个详情页
    if links:
        print(f"\n4. 测试获取详情页...")
        test_url = links[0]['url']
        success, detail_content = fetch_webpage(test_url)
        if success:
            print(f"✓ 详情页获取成功，内容长度: {len(detail_content)} 字符")
            
            # 简单提取标题
            soup = BeautifulSoup(detail_content, 'html.parser')
            title = soup.find('title')
            if title:
                print(f"  页面标题: {title.get_text().strip()}")
        else:
            print("✗ 详情页获取失败")
    
    # 保存结果到CSV
    print(f"\n5. 保存结果到CSV文件...")
    try:
        with open('test_results.csv', 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['标题', '日期', 'URL']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for link in links:
                writer.writerow({
                    '标题': link['title'],
                    '日期': link['date'],
                    'URL': link['url']
                })
        
        print(f"✓ 结果已保存到 test_results.csv，共 {len(links)} 条记录")
        return True
        
    except Exception as e:
        print(f"✗ 保存CSV失败: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        success = test_basic_functionality()
        if success:
            print(f"\n✓ 测试完成！基本功能正常")
        else:
            print(f"\n✗ 测试失败")
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")
