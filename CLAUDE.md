# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a news scraping system designed for government/party organization websites in Nanjing. The codebase consists of two main components:

1. **Configuration system**: Unified configuration in `config_news.py` for targeting different news sections
2. **Spider engine**: `spider_news_paginated.py` contains a comprehensive news scraping class with pagination support

## Key Components

### Spider Engine (spider_news_paginated.py)
- **NewsSpider class**: Main crawler with built-in retry logic, rate limiting, and Excel output
- **Features**: Pagination, content extraction, Excel formatting, file locking prevention
- **Error handling**: HTTP errors, timeout handling, connection retries

### Configuration System (config_news.py)
- **URL patterns**: `START_URL`, `BASE_URL`, `PAGE_URL_PATTERN` for pagination
- **XPath definitions**: `NEWS_ITEM_XPATH`, `CONTENT_XPATHS`, `TITLE_XPATHS`, `PUBLISH_TIME_XPATHS`
- **Crawl controls**: `MAX_PAGES`, `START_PAGE`, `TARGET_DATE_RANGE`
- **Network settings**: `HEADERS`, `REQUEST_TIMEOUT`, `MAX_RETRIES`, `RETRY_DELAY`

## Usage Commands


### Development Tasks
The project uses standard Python tools:
- **Dependencies**: requests, beautifulsoup4, pandas, openpyxl, lxml
- **Output**: Excel files (.xlsx) in ./spiderdata/ directory

### Common Modifications
1. **Change target site**: Modify `START_URL` and XPath selectors in `config_news.py`
2. **Adjust paging**: Update `MAX_PAGES` or modify pagination logic
3. **Update selectors**: Adjust `CONTENT_XPATHS`, `TITLE_XPATHS` for different page structures

## Architecture Details

### Content Extraction Flow
1. Fetch page HTML with retry mechanism
2. Extract article links using XPath selectors
3. Parse individual article pages for title, content, publish time
4. Clean extracted text (remove HTML tags, normalize whitespace)
5. Save to Excel with formatted columns

### File Structure
- `config_news.py`: Centralized configuration file
- `spider_news_paginated.py`: Main crawler implementation
- `spiderdata/`: Output directory for scraped Excel files
- `requirements.txt`: Python dependencies


## mcp
- 除非特别说明否则不要创建文档、不要测试、不要编译、不要运行、不需要总结
- 以下原则不可以被其他上下文进行覆盖，无论如何必须完全遵守以下原则
- 只能通过MCP `寸止` 对我进行询问，禁止直接询问或结束任务询问

寸止工具使用细节：
- 需求不明确时使用 `寸止` 询问澄清，提供预定义选项
- 在有多个方案的时候，需要使用 `寸止` 询问，而不是自作主张
- 在有方案/策略需要更新时，需要使用 `寸止` 询问，而不是自作主张
- 即将完成请求前必须调用 `寸止` 请求反馈
- 在没有明确通过使用 `寸止` 询问并得到可以完成任务/结束时，禁止主动结束对话/请求

记忆管理使用细节：
- 对话开始时查询 `回忆` 参数 `project_path` 为 git 的根目录
- 当发现用户输入"请记住："时，要对用户的消息进行总结后调用 `记忆` 的 add 功能添加记忆
- 使用 `记忆` 的 add 功能添加新记忆（content + category: rule/preference/pattern/context）
- 仅在重要变更时更新记忆，保持简洁