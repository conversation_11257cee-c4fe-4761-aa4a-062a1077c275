'''
Description: 
Date: 2025-06-26 15:37:32
Author: <PERSON><PERSON><PERSON>
LastEditTime: 2025-06-27 09:22:52
FilePath: \Spider_tzb\config_news.py
'''
# -*- coding: utf-8 -*-
"""
南京欧美同学会新闻分页爬虫配置文件
"""

# 起始页面配置
START_URL = "https://www.njwrsa.org.cn/news/7/0.html"
BASE_URL = "https://www.njwrsa.org.cn"

# XPath配置
LINKS_XPATH = "/html/body/div[3]/div/div[2]"  # 链接容器的XPath
NEXT_PAGE_XPATH = "//a[contains(text(), '下一页')]/@href"  # 下一页链接XPath

# 爬取配置
MAX_PAGES = 7  # 最多爬取页数
START_PAGE = 0  # 起始页码

# 输出文件配置
OUTPUT_FILENAME = "南京欧美同学会_本会动态.xlsx"

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 2  # 重试间隔（秒）

# SSL证书验证
VERIFY_SSL = True

# 代理设置
PROXIES = None

# 内容提取XPath配置（用于详情页）
CONTENT_XPATHS = [
    "/html/body/div[3]/div/div[2]/div/div[3]",  # 用户指定的正文内容XPath
]

# 标题提取XPath配置
TITLE_XPATHS = [
    "//title",
    "//h1",
    "//h2",
    "//div[@class='title']",
    "//div[contains(@class, 'title')]"
]

# 发表时间提取XPath配置
PUBLISH_TIME_XPATHS = [
    "/html/body/div[3]/div/div[2]/div/div[4]/h4/text()[2]",  # 用户指定的发表时间XPath
]
